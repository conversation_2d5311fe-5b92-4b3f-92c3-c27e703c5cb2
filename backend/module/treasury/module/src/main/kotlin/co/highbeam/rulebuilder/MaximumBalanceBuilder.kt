package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.MaximumBalance as RuleConfig

data class MaximumBalanceBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.BookTransfer(
          fromAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
            ?: BankAccountSelector.Primary,
          toAccount = BankAccountSelector.HighYield,
          amount = AmountSelector.Surplus(
            threshold = rule.maximumBalance,
            bankAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
              ?: BankAccountSelector.Primary,
          ),
          description = "Smart Yield - Maintain target balance",
        )
      )
    )
  }

  override suspend fun build(rule: RuleConfig, businessGuid: UUID, triggerData: TriggerData) =
    build(
      rule = rule.copy(bankAccountGuid = rule.bankAccountGuid ?: triggerData.bankAccountGuid),
      businessGuid = businessGuid
    )
}
