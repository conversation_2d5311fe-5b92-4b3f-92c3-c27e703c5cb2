package com.taktile.client

import co.highbeam.protectedString.ProtectedString
import com.taktile.rep.DecisionRequest
import com.taktile.rep.DecisionResponse
import io.ktor.http.HttpMethod

class TaktileClient(
  httpClient: TaktileHttpClient,
  taktileApiKey: ProtectedString,
  val environment: TaktileHttpClient.Environment,
) {
  private val request: TaktileRequest = TaktileRequest(
    httpClient,
    taktileApiKey,
  )

  suspend fun <T> decide(
    flowSlug: String,
    decisionRequest: DecisionRequest<T>
  ): DecisionResponse {
    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = when (environment) {
        TaktileHttpClient.Environment.Sandbox -> "/run/api/v1/flows/$flowSlug/sandbox/decide"
        TaktileHttpClient.Environment.Production -> "/run/api/v1/flows/$flowSlug/decide"
      },
      body = decisionRequest
    )

    // Handle the case where readText() returns null (e.g., 404 responses)
    val responseText = response.readText()
    if (responseText == null) {
      throw co.highbeam.client.exception.HighbeamHttpClientException(
        response.statusCode,
        "Taktile API returned empty response. This may indicate an invalid flow slug, API key, or endpoint configuration."
      )
    }

    if (responseText.isEmpty()) {
      throw co.highbeam.client.exception.HighbeamHttpClientException(
        response.statusCode,
        "Taktile API returned empty response body"
      )
    }

    return response.objectMapper.readValue(responseText)
  }
}
